using UnityEngine;

[System.Serializable]
public class BankLevel
{
    public GameObject[] gameObjects;
}

public class Building : MonoBehaviour
{
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    public int level = 0;
    public BankLevel[] bankLevels;

    void Start()
    {
        // Hide all levels
        foreach (BankLevel bankLevel in bankLevels)
        {
            foreach (GameObject obj in bankLevel.gameObjects)
            {
                obj.SetActive(false);
            }
        }
        // Show the current level
        if (level < bankLevels.Length && bankLevels[level].gameObjects != null)
        {
            foreach (GameObject obj in bankLevels[level].gameObjects)
            {
                obj.SetActive(true);
            }
        }
    }
    public void SetBankLevel(BankLevel[] bankLevels){
        this.bankLevels = bankLevels;
        Start();
    }
    public void NextLevel()
    {
        if (level < bankLevels.Length - 1)
        {
            level++;
            Start();
        }
    }
    public void PreviousLevel()
    {
        if (level > 0)
        {
            level--;
            Start();
        }
    }
    public void FirstLevel()
    {
        level = 0;
        Start();
    }
    public void LastLevel()
    {
        level = bankLevels.Length - 1;
        Start();
    }
}

