using UnityEngine;

public class BuildingManager : MonoBehaviour
{
    public Building[] buildings;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        buildings = new Building[transform.childCount];
        foreach (Transform child in transform)
        {
            buildings[child.GetSiblingIndex()] = child.GetComponent<Building>();
        }
    }

    
}
